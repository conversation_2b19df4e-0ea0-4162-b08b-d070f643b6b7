fact_id: fact_3

fact_format: |
  On [Date], [Company X] was among the highest free float stock with [X]% from the issued stocks, while [company Y] was among the lowest with [Z]%.

required_tables:
  - positive-theme-384616.standarized_dataset.stock_data

metrics:
  - name: date
    type: DATE
    description: Five different selected transaction dates from our dataset
  - name: company_name_high
    type: STRING
    description: Company with the highest free float percentage on each selected date
  - name: company_name_low
    type: STRING
    description: Company with the lowest free float percentage on the same selected date
  - name: float_percentage_high
    type: FLOAT
    description: The free float percentage of the company with the highest free float percentage on the selected date
  - name: float_percentage_low
    type: FLOAT
    description: The free float percentage of the company with the lowest free float percentage on the selected date

analysis_description: |
  The Free Float Analysis Process starts by cleaning the data, removing any rows with NULL float percentages.
  For each transaction date, companies are ranked by their float percentage, identifying both the highest and lowest values.
  The analysis then joins these high and low values for each date, ensuring different companies are selected for high and low positions.
  To ensure variety in the results, companies are deduplicated using random selection, and the final selection is limited to one entry per month.
  The process returns five random dates, each with a pair of companies representing the highest and lowest float percentages.

  The generated SQL query will return 5 rows - each containing a date, the company with highest float percentage,
  its float percentage, the company with lowest float percentage, and its float percentage.
  The query ensures all returned rows have complete data with no NULL values.

sql_query: |
  WITH
    cleaned_data AS (
      SELECT
        transaction_date,
        company_name,
        float_percentage
      FROM `positive-theme-384616.standarized_dataset.stock_data`
      WHERE
        float_percentage IS NOT NULL
        AND transaction_date >= '2023-01-01'
        AND transaction_date >= DATE_SUB((SELECT MAX(transaction_date) FROM positive-theme-384616.standarized_dataset.stock_data), INTERVAL 3 MONTH)
    ),

    ranked_floats AS (
      SELECT
        transaction_date,
        company_name,
        float_percentage,
        ROW_NUMBER() OVER (
          PARTITION BY transaction_date
          ORDER BY float_percentage DESC
        ) AS rn_high,
        ROW_NUMBER() OVER (
          PARTITION BY transaction_date
          ORDER BY float_percentage ASC
        ) AS rn_low
      FROM cleaned_data
    ),

    high_low_per_date AS (
      SELECT
        h.transaction_date AS date,
        h.company_name AS company_name_high,
        h.float_percentage AS float_percentage_high,
        l.company_name AS company_name_low,
        l.float_percentage AS float_percentage_low
      FROM (
        SELECT transaction_date, company_name, float_percentage
        FROM ranked_floats
        WHERE rn_high = 1
      ) AS h
      JOIN (
        SELECT transaction_date, company_name, float_percentage
        FROM ranked_floats
        WHERE rn_low = 1
      ) AS l
        ON h.transaction_date = l.transaction_date
        AND h.company_name <> l.company_name
    ),

    deduped AS (
      SELECT
        date,
        company_name_high,
        float_percentage_high,
        company_name_low,
        float_percentage_low
      FROM high_low_per_date
      QUALIFY
        ROW_NUMBER() OVER (
          PARTITION BY company_name_high
          ORDER BY RAND()
        ) = 1
        AND ROW_NUMBER() OVER (
          PARTITION BY company_name_low
          ORDER BY RAND()
        ) = 1
    ),

    combined AS (
      SELECT
        date,
        company_name_high,
        float_percentage_high,
        company_name_low,
        float_percentage_low,
        1 AS priority
      FROM deduped

      UNION ALL

      SELECT
        date,
        company_name_high,
        float_percentage_high,
        company_name_low,
        float_percentage_low,
        2 AS priority
      FROM high_low_per_date
    ),

    monthly_bucket AS (
      SELECT
        *,
        FORMAT_DATE('%Y-%m', date) AS month_group,
        ROW_NUMBER() OVER (
          PARTITION BY FORMAT_DATE('%Y-%m', date)
          ORDER BY RAND()
        ) AS rn_month
      FROM combined
    )

  SELECT
    date,
    company_name_high,
    float_percentage_high,
    company_name_low,
    float_percentage_low
  FROM monthly_bucket
  WHERE rn_month = 1
  ORDER BY priority, RAND()
  LIMIT 5; 