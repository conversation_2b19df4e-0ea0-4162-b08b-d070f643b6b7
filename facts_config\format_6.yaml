fact_id: fact_6

fact_format: |
  [Company X] was among the most active stocks in terms of liquidity for the past 52 weeks with total traded value [Y] SAR in the last 52 weeks.

required_tables:
  - positive-theme-384616.standarized_dataset.stock_data

metrics:
  - name: company_name
    type: STRING
    description: Name of the company with the highest liquidity for a specific reference date
  - name: total_traded_value
    type: FLOAT
    description: Sum of all traded values (turnover) for the company over the past 52 weeks from the reference date

analysis_description: |
  The Liquidity Activity Analysis assesses trading turnover over the past 52 weeks for all companies.
  For each company, total turnover is aggregated across the full period. The company with the highest cumulative
  turnover is flagged as the most active in terms of liquidity, with it's date noted on when it was most active
  starting from Latest date.

  The generated SQL query will return 5 rows - each row containing 'company_name', and 'total_traded_value' columns - but also make sure the returned rows don't have
  any NaN value in it.

sql_query: |
  -- This query identifies the top 5 most active companies based on their total traded value (turnover)
  -- over the past 52 weeks, calculated from the latest transaction date in the dataset.
  -- It ensures that all data used in calculations is valid (not NULL and not NaN).
  WITH 
    -- CTE to determine the latest transaction date in the dataset and
    -- calculate the start date of the 52-week lookback period.
    DateParameters AS (
      SELECT 
        MAX(transaction_date) AS latest_date,
        DATE_SUB(MAX(transaction_date), INTERVAL 52 WEEK) AS start_date_52_weeks
      FROM `positive-theme-384616.standarized_dataset.stock_data`
    ),
    
    -- CTE to calculate the sum of 'turnover' for each company within the 52-week window.
    -- Filters out records with NULL or NaN turnover values, and NULL company names.
    CompanyTotalTurnover AS (
      SELECT 
        s.company_name,
        SUM(s.turnover) AS total_traded_value_52_weeks
      FROM `positive-theme-384616.standarized_dataset.stock_data` AS s
      CROSS JOIN DateParameters AS dp
      WHERE 
        -- Filter transactions within the 52-week period.
        -- If latest_date or start_date_52_weeks is NULL (e.g., empty table), this condition will not pass
        s.transaction_date >= dp.start_date_52_weeks
        AND s.transaction_date <= dp.latest_date
        AND s.company_name IS NOT NULL
        AND s.turnover IS NOT NULL
        AND NOT IS_NAN(s.turnover)  -- Ensure turnover is a valid, non-NULL, non-NaN number
      GROUP BY s.company_name
    ),
    
    -- CTE to rank companies based on their calculated total_traded_value_52_weeks.
    -- Filters out companies if their aggregated turnover is NULL or NaN
    RankedCompanies AS (
      SELECT 
        company_name,
        total_traded_value_52_weeks,
        -- Using DENSE_RANK to handle ties in turnover; companies with the same turnover get the same rank
        DENSE_RANK() OVER (ORDER BY total_traded_value_52_weeks DESC) AS liquidity_rank
      FROM CompanyTotalTurnover
      WHERE 
        total_traded_value_52_weeks IS NOT NULL
        AND NOT IS_NAN(total_traded_value_52_weeks)  -- Ensure aggregated sum is valid
    )

  -- Final selection of the top 5 companies.
  -- Returns company_name and their total_traded_value over the 52-week period.
  -- Ordered by rank and then by company_name for deterministic results in case of ties in rank.
  SELECT 
    company_name,
    total_traded_value_52_weeks AS total_traded_value  -- Alias to match 'REQUIRED METRICS'
  FROM RankedCompanies
  WHERE liquidity_rank <= 5
  ORDER BY 
    liquidity_rank ASC,
    company_name ASC
  LIMIT 5;