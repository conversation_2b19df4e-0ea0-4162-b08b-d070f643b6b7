fact_id: fact_4

fact_format: |
  On [Date], [Company X] was the top gainer in terms of % for the past 52 weeks with [X]% increase in the last 52 weeks.

required_tables:
  - positive-theme-384616.standarized_dataset.pct_top_gainers

metrics:
  - name: date_highest_52w
    type: DATE
    description: The date within the past 52 weeks (from latest transaction_date) when the company is top gainer
  - name: company_name
    type: STRING
    description: Name of the company that is top gainer
  - name: pct_gain_52w
    type: FLOAT
    description: Percentage increase in the last 52 weeks

analysis_description: |
  The Top 52-Week Gainer Analysis identifies the company with the highest percentage gain over the past 52 weeks for each transaction date.
  Using the pre-calculated pct_gain_52w field, we filter records where rank_52w_gain = 1 to find the top performer.
  The analysis selects five random transaction dates and retrieves the corresponding top-gaining company for each date.

  The generated SQL query will return 5 rows - 5 unique top gainer companies for different dates. The query will ensure all returned rows have complete data with no NULL or NaN values in any field.

sql_query: |
  WITH ValidTopGainers AS (
    SELECT 
      transaction_date,
      company_name,
      pct_gain_52w
    FROM `positive-theme-384616.standarized_dataset.pct_top_gainers`
    WHERE rank_52w_gain = 1
      AND transaction_date IS NOT NULL
      AND company_name IS NOT NULL
      AND pct_gain_52w IS NOT NULL
      AND NOT IS_NAN(pct_gain_52w)
  ),
  UniqueTopGainerPerDate AS (
    SELECT 
      transaction_date,
      company_name,
      pct_gain_52w
    FROM (
      SELECT 
        transaction_date,
        company_name,
        pct_gain_52w,
        ROW_NUMBER() OVER (
          PARTITION BY transaction_date
          ORDER BY company_name ASC
        ) AS rn_per_date
      FROM ValidTopGainers
    )
    WHERE rn_per_date = 1
    AND transaction_date >= DATE_SUB((SELECT MAX(transaction_date) FROM positive-theme-384616.standarized_dataset.pct_top_gainers), INTERVAL 3 MONTH)
  ),
  RandomlySelectedGainers AS (
    SELECT 
      transaction_date,
      company_name,
      pct_gain_52w
    FROM UniqueTopGainerPerDate
    ORDER BY RAND()
    LIMIT 5
  )
  SELECT 
    transaction_date AS date_highest_52w,
    company_name,
    pct_gain_52w
  FROM RandomlySelectedGainers
  ORDER BY date_highest_52w;