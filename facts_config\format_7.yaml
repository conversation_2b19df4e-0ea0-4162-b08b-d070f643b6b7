fact_id: fact_7

fact_format: |
  On [Date], [Company X] reached its lowest stock price in 52 weeks, with change [Z]% from the beginning of year.

required_tables:
  - positive-theme-384616.standarized_dataset.stock_data

metrics:
  - name: date_52w_low
    type: DATE
    description: The specific date within the past 52 weeks when the company reached its lowest closing stock price
  - name: company_name
    type: STRING
    description: Name of the company
  - name: pct_change_ytd
    type: FLOAT
    description: Percentage change between the lowest closing price and the year's starting price


analysis_description: |
  The 52-Week Low Analysis Process begins by filtering to include only companies with complete data coverage spanning the full 52-week lookback period
  from the latest transaction date, excluding any companies with insufficient historical records. For qualifying companies,
  the analysis identifies the specific date when each company recorded its lowest closing price within this 52-week window.
  The process then calculates the percentage change from the beginning of the year in which this lowest point occurred,
  using the formula: ((lowest_close - year_start_close) / year_start_close) * 100,
  quantifying the performance relative to the year's starting price.

  The generated SQL query will return 5 rows - 5 companies selected randomly - with each row containing
  the date when that company reached its lowest closing stock price in the 52-week period but also make sure the returned rows don't have
  any NaN value in it.

sql_query: |
  -- This query identifies companies that reached their 52-week low price
  -- within a 52-week window ending on the latest transaction date in the dataset.
  -- For these companies, it calculates the percentage change from the closing price
  -- at the start of the year (in which the 52-week low occurred) to this 52-week low price.
  -- The query filters for companies with complete data coverage over the 52-week period.
  -- It returns 5 randomly selected companies that meet all criteria, ensuring no NULL/NaN values
  -- for the calculated percentage change.
  WITH 
    -- 1. Determine the latest transaction date in the dataset to define the end of our analysis window
    MaxDate AS (
      SELECT MAX(transaction_date) AS latest_date
      FROM `positive-theme-384616.standarized_dataset.stock_data`
    ),
    
    -- 2. Define the 52-week lookback window boundaries based on the latest_date
    WindowBoundaries AS (
      SELECT 
        latest_date,
        -- A 52-week period is 364 days (52 weeks * 7 days/week). The window is inclusive.
        DATE_SUB(latest_date, INTERVAL 364 DAY) AS start_date_52w
      FROM MaxDate
    ),
    
    -- 3. Identify all distinct trading days within this global 52-week window from the dataset
    GlobalTradingDaysInWindow AS (
      SELECT DISTINCT d.transaction_date
      FROM `positive-theme-384616.standarized_dataset.stock_data` d
      CROSS JOIN WindowBoundaries wb -- WindowBoundaries has one row, so CROSS JOIN is efficient
      WHERE d.transaction_date BETWEEN wb.start_date_52w AND wb.latest_date
    ),
    
    CountGlobalTradingDays AS (
      SELECT COUNT(transaction_date) AS total_distinct_trading_days
      FROM GlobalTradingDaysInWindow
    ),
    
    -- 4. For each company, count its number of trading days within the 52-week window
    CompanyTradingDaysInWindow AS (
      SELECT 
        d.ticker_id,
        COUNT(DISTINCT d.transaction_date) AS num_company_trading_days
      FROM `positive-theme-384616.standarized_dataset.stock_data` d
      CROSS JOIN WindowBoundaries wb
      WHERE d.transaction_date BETWEEN wb.start_date_52w AND wb.latest_date
      GROUP BY d.ticker_id
    ),
    
    -- 5. Filter for companies that have "complete data coverage" - meaning they have data
    --    for every single trading day identified in the GlobalTradingDaysInWindow.
    EligibleCompanies AS (
      SELECT ctd.ticker_id
      FROM CompanyTradingDaysInWindow ctd
      CROSS JOIN CountGlobalTradingDays cgtd -- CountGlobalTradingDays has one row
      WHERE ctd.num_company_trading_days = cgtd.total_distinct_trading_days
        AND cgtd.total_distinct_trading_days > 0 -- Ensure the window itself has trading days
    ),
    
    -- 6. Filter the main dataset for eligible companies and data points falling within the 52-week window
    DataInWindow AS (
      SELECT 
        d.company_name,
        d.ticker_id,
        d.transaction_date,
        d.close
      FROM `positive-theme-384616.standarized_dataset.stock_data` d
      JOIN EligibleCompanies ec ON d.ticker_id = ec.ticker_id
      CROSS JOIN WindowBoundaries wb
      WHERE d.transaction_date BETWEEN wb.start_date_52w AND wb.latest_date
      AND d.transaction_date >= DATE_SUB(wb.latest_date, INTERVAL 3 MONTH)
    ),
    
    -- 7. For each eligible company, rank its daily closing prices within the window to find the lowest.
    --    If multiple days share the same lowest price, the earliest date is chosen.
    RankedByPriceInWindow AS (
      SELECT 
        company_name,
        ticker_id,
        transaction_date,
        CLOSE,
        ROW_NUMBER() OVER (
          PARTITION BY ticker_id
          ORDER BY CLOSE ASC, transaction_date ASC
        ) AS rn_low_price
      FROM DataInWindow
      WHERE CLOSE IS NOT NULL -- Consider only non-null closing prices for ranking
    ),
    
    -- 8. Select the record representing the 52-week low for each company
    FiftyTwoWeekLows AS (
      SELECT 
        company_name,
        ticker_id,
        transaction_date AS date_52w_low,
        CLOSE AS price_52w_low
      FROM RankedByPriceInWindow
      WHERE rn_low_price = 1
    ),
    
    -- 9. Determine the closing price on the first trading day of the year for each company.
    --    This is used as the base for YTD calculations.
    CompanyYearStartRawData AS (
      SELECT 
        d.ticker_id,
        d.transaction_date, -- Needed for the DISTINCT selection logic below
        EXTRACT(YEAR FROM d.transaction_date) AS txn_year,
        -- Use FIRST_VALUE to get the close price on the first transaction_date of the year for that ticker
        FIRST_VALUE(d.close IGNORE NULLS) OVER (
          PARTITION BY d.ticker_id, EXTRACT(YEAR FROM d.transaction_date)
          ORDER BY d.transaction_date ASC
        ) AS year_start_close,
        -- Also get the first transaction_date itself to ensure we're using the price from that specific day
        FIRST_VALUE(d.transaction_date IGNORE NULLS) OVER (
          PARTITION BY d.ticker_id, EXTRACT(YEAR FROM d.transaction_date)
          ORDER BY d.transaction_date ASC
        ) AS first_trading_date_of_year
      FROM `positive-theme-384616.standarized_dataset.stock_data` d
      WHERE d.close IS NOT NULL -- Ensure that the close price used for year_start_close is not NULL
    ),
    
    -- Consolidate to one year-start price per company per year.
    -- Filter out null or zero start prices to prevent issues in percentage calculation.
    DistinctYearStartPrices AS (
      SELECT DISTINCT 
        ticker_id,
        txn_year,
        year_start_close
      FROM CompanyYearStartRawData
      WHERE transaction_date = first_trading_date_of_year -- Critical: ensures this IS the price from the first day
        AND year_start_close IS NOT NULL
        AND year_start_close <> 0 -- Essential to avoid division by zero for pct_change_ytd
    ),
    
    -- 10. Combine the 52-week low data with the year-start prices and calculate the YTD percentage change.
    FinalCalculations AS (
      SELECT 
        lows.company_name,
        lows.date_52w_low,
        -- Calculate YTD percentage change: ((current_price - start_price) / start_price) * 100
        -- Rounded to 2 decimal places.
        -- dysp.year_start_close is already guaranteed to be non-null and non-zero by DistinctYearStartPrices CTE.
        ROUND(((lows.price_52w_low - dysp.year_start_close) / dysp.year_start_close) * 100, 2) AS pct_change_ytd
      FROM FiftyTwoWeekLows lows
      JOIN DistinctYearStartPrices dysp 
        ON lows.ticker_id = dysp.ticker_id
        AND EXTRACT(YEAR FROM lows.date_52w_low) = dysp.txn_year -- Match on company and the year of the low
    )
  
  -- 11. Select the final required metrics.
  --     Filter out any rows where pct_change_ytd could not be calculated (e.g., if year_start_close was missing for that year).
  --     Return 5 randomly selected rows.
  SELECT 
    date_52w_low,
    company_name,
    pct_change_ytd
  FROM FinalCalculations
  WHERE pct_change_ytd IS NOT NULL -- Final check to ensure the percentage is valid and not NULL
  ORDER BY RAND() -- For random selection of 5 companies
  LIMIT 5;