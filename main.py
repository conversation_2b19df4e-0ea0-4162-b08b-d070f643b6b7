import os
import json
import yaml
import datetime
import random
import time
import logging
from pathlib import Path
from datetime import date
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from functools import lru_cache
from flask import Flask, request, jsonify
from flask_cors import CORS
from google.cloud import bigquery, storage
from google.oauth2 import service_account
from langchain_google_genai import ChatGoogleGenerativeAI
from dotenv import load_dotenv
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

load_dotenv()

# LangSmith Configuration
os.environ["LANGSMITH_TRACING"] = "true"
os.environ["LANGSMITH_ENDPOINT"] = "https://api.smith.langchain.com"
os.environ["LANGSMITH_API_KEY"] = os.getenv("LANGSMITH_API_KEY")
os.environ["LANGSMITH_PROJECT"] = os.getenv("LANGSMITH_PROJECT", "awrag-random-facts")

# Configuration
os.environ["GOOGLE_API_KEY"] = os.getenv("GOOGLE_API_KEY")
SERVICE_ACCOUNT_PATH = os.getenv("SERVICE_ACCOUNT_PATH", "positive-theme-384616-d5b81c5912ab.json")

# Cloud Storage Configuration
BUCKET_NAME = os.getenv("FACTS_CACHE_BUCKET", "random-facts-cache")
CACHE_PREFIX = "facts_cache"

# Language configuration
SUPPORTED_LANGUAGES = {
    "en": "English",
    "ar": "Arabic",
    "both": "Both English and Arabic"
}

# -------------------------------
# Setup
# -------------------------------
app = Flask(__name__)
CORS(app)

# Configure Flask to handle Unicode properly
app.config['JSON_AS_ASCII'] = False
app.config['JSONIFY_MIMETYPE'] = 'application/json; charset=utf-8'

# Logging setup
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s [%(name)s] %(message)s"
)
logger = logging.getLogger("fact_generator")

# Load credentials with expanded scopes
try:
    credentials = service_account.Credentials.from_service_account_file(
        SERVICE_ACCOUNT_PATH,
        scopes=["https://www.googleapis.com/auth/bigquery", 
               "https://www.googleapis.com/auth/cloud-platform"]
    )
    bq_client = bigquery.Client(credentials=credentials, project=credentials.project_id)
    storage_client = storage.Client(credentials=credentials, project=credentials.project_id)
    bucket = storage_client.bucket(BUCKET_NAME)
    print(f"✅ BigQuery client initialized for project: {credentials.project_id}")
    print(f"✅ Cloud Storage client initialized for bucket: {BUCKET_NAME}")
except Exception as e:
    print(f"❌ Error initializing clients: {e}")
    bq_client = None
    storage_client = None
    bucket = None

# Initialize LLM
try:
    llm = ChatGoogleGenerativeAI(model="gemini-2.0-flash", temperature=0.1)
    print("✅ LLM initialized successfully")
except Exception as e:
    print(f"❌ Error initializing LLM: {e}")
    llm = None

# -------------------------------
# Cache Management Functions
# -------------------------------

def get_cache_blob_path(language: str) -> str:
    """Get the Cloud Storage blob path for today's facts."""
    today = date.today().isoformat()
    return f"{CACHE_PREFIX}/facts_{today}_{language}.json"

def get_cache_metadata_blob_path() -> str:
    """Get the path for cache metadata blob."""
    return f"{CACHE_PREFIX}/cache_metadata.json"

def load_cache_metadata() -> dict:
    """Load cache metadata including last generation time from Cloud Storage."""
    if not bucket:
        return {"last_generated": None, "language": None}
        
    blob = bucket.blob(get_cache_metadata_blob_path())
    if not blob.exists():
        return {"last_generated": None, "language": None}
    
    try:
        content = blob.download_as_text()
        return json.loads(content)
    except (json.JSONDecodeError, Exception) as e:
        logger.warning(f"Failed to load cache metadata: {e}")
        return {"last_generated": None, "language": None}

def save_cache_metadata(metadata: dict):
    """Save cache metadata to Cloud Storage."""
    if not bucket:
        return
        
    try:
        blob = bucket.blob(get_cache_metadata_blob_path())
        blob.upload_from_string(
            json.dumps(metadata, indent=2, ensure_ascii=False),
            content_type='application/json; charset=utf-8'
        )
        logger.info("Cache metadata saved successfully")
    except Exception as e:
        logger.error(f"Failed to save cache metadata: {e}")

def load_cached_facts(language: str) -> dict:
    """Load today's cached facts from Cloud Storage if available."""
    if not bucket:
        return {}
        
    blob = bucket.blob(get_cache_blob_path(language))
    if not blob.exists():
        logger.info(f"No cached facts found for {language}")
        return {}
    
    try:
        content = blob.download_as_text()
        cached_facts = json.loads(content)
        logger.info(f"Loaded cached facts for {language}: {len(cached_facts)} categories")
        return cached_facts
    except (json.JSONDecodeError, Exception) as e:
        logger.warning(f"Failed to load cached facts: {e}")
        return {}

def save_cached_facts(facts: dict, language: str):
    """Save facts to today's cache file in Cloud Storage."""
    if not bucket:
        return
        
    try:
        blob = bucket.blob(get_cache_blob_path(language))
        blob.upload_from_string(
            json.dumps(facts, indent=2, ensure_ascii=False),
            content_type='application/json; charset=utf-8'
        )
        logger.info(f"Cached facts saved for {language}: {len(facts)} categories")
    except Exception as e:
        logger.error(f"Failed to save cached facts: {e}")

# -------------------------------
# Load fact configs (YAML files)
# -------------------------------
FACTS_CONFIG_DIR = Path(__file__).parent / "facts_config"

@lru_cache(maxsize=1)
def load_fact_specs():
    """Load fact specifications from YAML files with caching."""
    specs = []
    print(f"📁 Looking for YAML files in: {FACTS_CONFIG_DIR}")
    
    if not FACTS_CONFIG_DIR.exists():
        print(f"❌ Directory {FACTS_CONFIG_DIR} does not exist")
        return specs
    
    yaml_files = list(FACTS_CONFIG_DIR.glob("*.yaml"))
    print(f"📄 Found {len(yaml_files)} YAML files: {[f.name for f in yaml_files]}")
    
    for f in yaml_files:
        try:
            with open(f, "r") as stream:
                spec = yaml.safe_load(stream)
                if spec:
                    print(f"✅ Loaded spec from {f.name}: {spec.get('name', 'unnamed')}")
                    # Validate required fields - check both possible field names
                    query_field = spec.get('query') or spec.get('sql_query')
                    if not query_field or not query_field.strip():
                        print(f"⚠️  Warning: {f.name} missing or empty 'query' or 'sql_query' field")
                    specs.append(spec)
                else:
                    print(f"⚠️  Warning: {f.name} is empty or invalid")
        except Exception as e:
            print(f"❌ Error loading {f.name}: {e}")
    
    print(f"📊 Total valid specs loaded: {len(specs)}")
    return specs

# Load fact specs at startup
fact_specs = load_fact_specs()

# -------------------------------
# Enhanced data fetching with caching
# -------------------------------
@lru_cache(maxsize=8)
def fetch_data_for_spec(spec_name: str, query: str):
    """Fetch data from BigQuery with caching."""
    if not bq_client:
        raise Exception("BigQuery client not initialized")
    
    if not query or not query.strip():
        raise Exception("Query is empty or None")
    
    start_time = time.time()
    print(f"🔍 Executing query for {spec_name}: {query[:100]}...")
    
    job = bq_client.query(query)
    rows = job.result()
    
    records = []
    for row in rows:
        rec = dict(row)
        # Serialize timestamps
        for k, v in rec.items():
            if hasattr(v, "isoformat"):
                rec[k] = v.isoformat()
        records.append(rec)
    
    elapsed = time.time() - start_time
    print(f"📊 [{spec_name}] fetched {len(records)} rows in {elapsed:.1f}s")
    return records

# -------------------------------
# Enhanced fact generation with language support
# -------------------------------

def get_language_prompt(language: str) -> str:
    """Get the language-specific prompt instructions."""
    if language == "en":
        return "Generate facts in English only."
    elif language == "ar":
        return "Generate facts in Saudi dialect (اللهجة السعودية) only."
    elif language == "both":
        return """
        Generate each fact in both English and Arabic (Saudi dialect).
        
        Format each fact as:
        {
            "statement": {
                "en": "English fact here",
                "ar": "Arabic fact here"
            }
        }
        """
    else:
        raise ValueError(f"Unsupported language: {language}")

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=1, max=20),
    retry=retry_if_exception_type(Exception),
    reraise=True
)
def generate_fact_from_spec_enhanced(spec, records: list, language: str = "both"):
    """Enhanced fact generation with language support and retry logic."""
    if not llm:
        raise Exception("LLM not initialized")
    
    if not records:
        return []
    
    language_instructions = get_language_prompt(language)
    
    # Define the response format based on language
    if language == "both":
        response_format = """
        {
            "facts": [
                {
                    "statement": {
                        "en": "English fact 1",
                        "ar": "Arabic fact 1"
                    }
                }
            ]
        }
        """
    else:
        response_format = """
        {
            "facts": [
                {
                    "statement": "fact 1"
                }
            ]
        }
        """
    
    # Get fact format from spec
    fact_format = spec.get('fact_format', spec.get('name', 'Financial fact'))
    
    prompt = f"""
    You are a financial analyst creating concise, user engaging and informative facts about stock market data.

    {language_instructions}

    Use the following data to create facts in the specified format:

    FACT FORMAT:
    "{fact_format}"

    DATA:
    {json.dumps(records, indent=2)}

    Generate exactly 5 facts from the given companies data.
    Maintain consistency in including the date, company name, and percentage values as specified in the format, but vary the connecting words and presentation style.
    Mention the month name properly.

    Format numbers appropriately:
    - For English: Use commas for thousands, 2 decimal places for percentages
    - For Arabic: Use Saudi number format (١٬٢٬٣) and proper decimal formatting
    - ALWAYS use suffixes (billion, million, thousand) for monetary values and prices

    Return the facts in the following JSON format:
    {response_format}

    Important: Return ONLY the JSON array with exactly 5 facts, no additional text.
    """
    
    start_time = time.time()
    try:
        response = llm.invoke(prompt)
        elapsed = time.time() - start_time
        spec_name = spec.get('name', 'unknown')
        logger.info(f"[{spec_name}] LLM fact generation completed in {elapsed:.1f} seconds")
        
        if not response or not response.content:
            raise ValueError("Empty response from LLM")
            
        text = response.content
        
        # Strip code fences if present
        if text.startswith("```"):
            text = "\n".join(text.splitlines()[1:-1])
        
        # Parse the facts
        try:
            facts_data = json.loads(text)
            if not isinstance(facts_data, dict) or "facts" not in facts_data:
                raise ValueError("Invalid JSON structure: missing 'facts' key")
                
            if not isinstance(facts_data["facts"], list):
                raise ValueError("Invalid JSON structure: 'facts' must be a list")
                
            # Extract just the statements and return them as a list
            return [fact["statement"] for fact in facts_data["facts"]]
                
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON response: {str(e)}")
            
    except Exception as e:
        spec_name = spec.get('name', 'unknown')
        logger.error(f"[{spec_name}] LLM error: {str(e)}")
        raise

# -------------------------------
# Main facts generation orchestrator
# -------------------------------
def generate_all_facts(language: str = "both", force_refresh: bool = False):
    """Generate or retrieve facts based on date and language with caching."""
    if language not in SUPPORTED_LANGUAGES:
        raise ValueError(f"Unsupported language: {language}")

    # Check if we need to regenerate facts
    metadata = load_cache_metadata()
    today = date.today().isoformat()
    
    should_regenerate = (
        force_refresh or
        not metadata.get("last_generated") or
        metadata["last_generated"] != today or
        metadata.get("language") != language
    )

    if not should_regenerate:
        logger.info(f"Using cached facts for today in {SUPPORTED_LANGUAGES[language]}")
        cached_facts = load_cached_facts(language)
        if cached_facts:
            return cached_facts

    logger.info(f"Generating new facts for today in {SUPPORTED_LANGUAGES[language]}")
    
    # Get valid specs
    valid_specs = [spec for spec in fact_specs if (spec.get("query", "").strip() or spec.get("sql_query", "").strip())]
    
    if not valid_specs:
        raise Exception("No valid fact specifications found")

    # Fetch data for all specs in parallel
    logger.info("Fetching fresh data from BigQuery...")
    spec_data = {}
    
    with ThreadPoolExecutor(max_workers=4) as executor:
        future_to_spec = {}
        for spec in valid_specs:
            query = spec.get("query") or spec.get("sql_query")
            spec_name = spec.get("name", spec.get("fact_id", "unknown"))
            future = executor.submit(fetch_data_for_spec, spec_name, query)
            future_to_spec[future] = spec
        
        for future in as_completed(future_to_spec):
            spec = future_to_spec[future]
            spec_name = spec.get("name", spec.get("fact_id", "unknown"))
            try:
                data = future.result()
                spec_data[spec_name] = data
                logger.info(f"[{spec_name}] Successfully fetched {len(data)} records")
            except Exception as e:
                logger.error(f"[{spec_name}] fetch error: {e}")
                spec_data[spec_name] = []

    # Generate facts for all specs in parallel
    all_facts = {}
    
    with ThreadPoolExecutor(max_workers=2) as executor:  # Reduced to avoid rate limits
        future_to_spec = {}
        for spec in valid_specs:
            spec_name = spec.get("name", spec.get("fact_id", "unknown"))
            if spec_name in spec_data and spec_data[spec_name]:
                future = executor.submit(
                    generate_fact_from_spec_enhanced,
                    spec,
                    spec_data[spec_name],
                    language
                )
                future_to_spec[future] = spec
        
        for future in as_completed(future_to_spec):
            spec = future_to_spec[future]
            spec_name = spec.get("name", spec.get("fact_id", "unknown"))
            fact_id = spec.get("fact_id", spec_name)
            
            try:
                facts = future.result()
                all_facts[fact_id] = facts
                logger.info(f"[{spec_name}] Generated {len(facts)} facts")
            except Exception as e:
                logger.error(f"[{spec_name}] fact generation error: {e}")
                all_facts[fact_id] = [f"Error generating facts for {spec_name}: {str(e)}" for _ in range(5)]

    # Save to cache
    try:
        save_cached_facts(all_facts, language)
        save_cache_metadata({
            "last_generated": today,
            "language": language
        })
    except Exception as e:
        logger.error(f"Failed to save to cache: {e}")
        # Continue with generated facts even if caching fails

    logger.info(f"Successfully generated facts in {SUPPORTED_LANGUAGES[language]}")
    return all_facts

def get_random_facts_from_all(all_facts: dict, count: int = 8, language: str = "both"):
    """Select random facts from all categories."""
    # Flatten all facts
    flat_facts = []
    for category_facts in all_facts.values():
        flat_facts.extend(category_facts)
    
    if not flat_facts:
        return []
    
    # Select random facts
    selected_count = min(count, len(flat_facts))
    selected_facts = random.sample(flat_facts, selected_count)
    
    return selected_facts

# -------------------------------
# Routes
# -------------------------------

@app.route("/", methods=["GET"])
def home():
    return jsonify({
        "message": "Random Facts API with Caching",
        "version": "2.0",
        "status": {
            "bigquery_client": "✅ Connected" if bq_client else "❌ Not connected",
            "cloud_storage": "✅ Connected" if bucket else "❌ Not connected", 
            "llm": "✅ Connected" if llm else "❌ Not connected",
            "fact_specs_loaded": len(fact_specs),
            "cache_bucket": BUCKET_NAME,
            "supported_languages": list(SUPPORTED_LANGUAGES.keys())
        },
        "endpoints": {
            "/facts": "GET - Retrieve random facts",
            "/facts?lang=en": "GET - Retrieve facts in English",
            "/facts?lang=ar": "GET - Retrieve facts in Arabic", 
            "/facts?lang=both": "GET - Retrieve bilingual facts",
            "/facts?count=5": "GET - Retrieve specific number of facts",
            "/facts?refresh=true": "GET - Force refresh facts cache",
            "/cache/status": "GET - Check cache status",
            "/cache/clear": "POST - Clear cache",
            "/debug": "GET - Debug information"
        },
        "example": "GET /facts?lang=both&count=3"
    })

@app.route("/debug", methods=["GET"])
def debug_info():
    cache_metadata = load_cache_metadata()
    
    return jsonify({
        "fact_specs": [
            {
                "name": spec.get("name", spec.get("fact_id", "unnamed")),
                "fact_id": spec.get("fact_id", "N/A"),
                "has_query": bool((spec.get("query", "").strip() or spec.get("sql_query", "").strip())),
                "query_preview": (spec.get("query") or spec.get("sql_query", ""))[:100] + "..." if (spec.get("query") or spec.get("sql_query")) else "No query"
            }
            for spec in fact_specs
        ],
        "config_directory": {
            "path": str(FACTS_CONFIG_DIR),
            "exists": FACTS_CONFIG_DIR.exists(),
            "yaml_files": [f.name for f in FACTS_CONFIG_DIR.glob("*.yaml")] if FACTS_CONFIG_DIR.exists() else []
        },
        "environment": {
            "google_api_key_set": bool(os.getenv("GOOGLE_API_KEY")),
            "service_account_path": SERVICE_ACCOUNT_PATH,
            "service_account_exists": Path(SERVICE_ACCOUNT_PATH).exists()
        },
        "cache": {
            "bucket_name": BUCKET_NAME,
            "bucket_accessible": bucket is not None,
            "metadata": cache_metadata
        }
    })

@app.route("/cache/status", methods=["GET"])
def cache_status():
    """Get cache status and metadata."""
    if not bucket:
        return jsonify({"error": "Cloud Storage not available"}), 500
    
    metadata = load_cache_metadata()
    today = date.today().isoformat()
    
    # Check what languages have cached facts for today
    cached_languages = {}
    for lang in SUPPORTED_LANGUAGES:
        blob = bucket.blob(get_cache_blob_path(lang))
        cached_languages[lang] = {
            "exists": blob.exists(),
            "is_today": metadata.get("last_generated") == today and metadata.get("language") == lang
        }
    
    return jsonify({
        "cache_metadata": metadata,
        "today": today,
        "cached_languages": cached_languages,
        "bucket_name": BUCKET_NAME
    })

@app.route("/cache/clear", methods=["POST"])
def clear_cache():
    """Clear all cached facts and metadata."""
    if not bucket:
        return jsonify({"error": "Cloud Storage not available"}), 500
    
    try:
        # Clear metadata
        metadata_blob = bucket.blob(get_cache_metadata_blob_path())
        if metadata_blob.exists():
            metadata_blob.delete()
        
        # Clear facts for all languages
        deleted_count = 0
        for lang in SUPPORTED_LANGUAGES:
            facts_blob = bucket.blob(get_cache_blob_path(lang))
            if facts_blob.exists():
                facts_blob.delete()
                deleted_count += 1
        
        return jsonify({
            "message": "Cache cleared successfully",
            "deleted_files": deleted_count + 1  # +1 for metadata
        })
    
    except Exception as e:
        return jsonify({"error": f"Failed to clear cache: {str(e)}"}), 500

@app.route("/health", methods=["GET"])
def health_check():
    return jsonify({
        "status": "healthy" if (bq_client and llm and fact_specs and bucket) else "unhealthy",
        "timestamp": datetime.datetime.utcnow().isoformat(),
        "components": {
            "bigquery_client": bool(bq_client),
            "cloud_storage": bool(bucket),
            "llm": bool(llm),
            "fact_specs_loaded": len(fact_specs)
        }
    })

@app.route("/facts", methods=["GET"])
def get_facts():
    try:
        # Check prerequisites
        if not bq_client:
            return jsonify({"error": "BigQuery client not initialized"}), 500
        
        if not llm:
            return jsonify({"error": "LLM not initialized"}), 500
        
        if not bucket:
            return jsonify({"error": "Cloud Storage not available"}), 500
        
        if not fact_specs:
            return jsonify({
                "error": "No fact specifications loaded",
                "help": f"Please add YAML files to {FACTS_CONFIG_DIR}",
                "debug_url": "/debug"
            }), 500

        # Get parameters
        lang = request.args.get("lang", "both")
        count = int(request.args.get("count", 8))
        force_refresh = request.args.get("refresh", "").lower() == "true"
        
        # Validate parameters
        if lang not in SUPPORTED_LANGUAGES:
            return jsonify({
                "error": f"Unsupported language: {lang}",
                "supported_languages": list(SUPPORTED_LANGUAGES.keys())
            }), 400
            
        if count <= 0 or count > 20:
            return jsonify({"error": "Count must be between 1 and 20"}), 400

        # Generate or retrieve facts
        all_facts = generate_all_facts(language=lang, force_refresh=force_refresh)
        
        if not all_facts:
            return jsonify({"error": "No facts could be generated"}), 500
        
        # Get random selection
        selected_facts = get_random_facts_from_all(all_facts, count, lang)
        
        response_data = {
            "facts": selected_facts,
            "language": lang,
            "language_name": SUPPORTED_LANGUAGES[lang],
            "total_available": sum(len(facts) for facts in all_facts.values()),
            "returned_count": len(selected_facts),
            "timestamp": datetime.datetime.utcnow().isoformat(),
            "cached": not force_refresh
        }

        # Create response with proper Unicode handling
        response = jsonify(response_data)
        response.headers['Content-Type'] = 'application/json; charset=utf-8'
        return response
    
    except ValueError as e:
        return jsonify({"error": f"Invalid parameter: {str(e)}"}), 400
    except Exception as e:
        logger.error(f"Error in get_facts: {str(e)}")
        return jsonify({
            "error": f"Internal server error: {str(e)}",
            "debug_url": "/debug"
        }), 500

# -------------------------------
# Run locally
# -------------------------------
if __name__ == "__main__":
    print("🚀 Starting Flask app with caching...")
    print("📋 Startup summary:")
    print(f"   BigQuery client: {'✅' if bq_client else '❌'}")
    print(f"   Cloud Storage: {'✅' if bucket else '❌'}")
    print(f"   LLM: {'✅' if llm else '❌'}")
    print(f"   Fact specs loaded: {len(fact_specs)}")
    print(f"   Config directory: {FACTS_CONFIG_DIR}")
    print(f"   Config directory exists: {FACTS_CONFIG_DIR.exists()}")
    print(f"   Cache bucket: {BUCKET_NAME}")
    print(f"   Supported languages: {list(SUPPORTED_LANGUAGES.keys())}")
    
    app.run(host="0.0.0.0", port=8080, debug=True)
