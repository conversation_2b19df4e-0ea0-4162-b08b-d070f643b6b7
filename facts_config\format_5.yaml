fact_id: fact_5

fact_format: |
  On [Date], [Company X] was the top loser in terms of % for the past 52 weeks with [X]% decrease in the last 52 weeks.

required_tables:
  - positive-theme-384616.standarized_dataset.pct_top_losers

metrics:
  - name: date_lowest_52w
    type: DATE
    description: The date within the past 52 weeks (from latest transaction_date) when the company is top loser
  - name: company_name
    type: STRING
    description: Name of the company that is top loser
  - name: pct_loss_52w
    type: FLOAT
    description: Percentage loss in the last 52 weeks

analysis_description: |
  The Top 52-Week Loser Analysis identifies the company with the greatest percentage decrease over the past 52 weeks for each transaction date.
  Using the pre-calculated pct_loss_52w field, we filter records for each date where rank_52w_loss equals the maximum rank value for that specific
  date, representing the worst performer. The analysis selects five random transaction dates FROM THE LAST 3 MONTHS, determines the highest rank
  number present on each date, and retrieves the company with that rank. The process verifies negative gain values (losses) and ensures data
  completeness before presenting the poorest-performing stocks across the selected timeframes.

  The generated SQL query must return 5 rows - 5 unique top loser companies from different dates within the last 3 months. 
  The query will ensure all returned rows have complete data with no NULL or NaN values in any field.

sql_query: |
  WITH RecentDates AS (
    -- First, get only dates from the last 3 months
    SELECT DISTINCT transaction_date
    FROM `positive-theme-384616.standarized_dataset.pct_top_losers`
    WHERE transaction_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 3 MONTH)
      AND transaction_date IS NOT NULL
  ),
  MaxRankPerDate AS (
    SELECT 
      t.transaction_date,
      MAX(t.rank_52w_loss) AS max_rank
    FROM `positive-theme-384616.standarized_dataset.pct_top_losers` t
    INNER JOIN RecentDates rd ON t.transaction_date = rd.transaction_date
    WHERE t.transaction_date IS NOT NULL
      AND t.company_name IS NOT NULL
      AND t.pct_loss_52w IS NOT NULL
      AND NOT IS_NAN(t.pct_loss_52w)
      AND t.pct_loss_52w < 0 -- A loss must be a negative percentage change
      AND t.rank_52w_loss IS NOT NULL
    GROUP BY t.transaction_date
  ),
  TopLoserPerDate AS (
    SELECT 
      t.transaction_date,
      t.company_name,
      t.pct_loss_52w,
      ROW_NUMBER() OVER (
        PARTITION BY t.transaction_date
        ORDER BY t.company_name ASC
      ) AS rn
    FROM `positive-theme-384616.standarized_dataset.pct_top_losers` t
    INNER JOIN RecentDates rd ON t.transaction_date = rd.transaction_date
    JOIN MaxRankPerDate mrpd 
      ON t.transaction_date = mrpd.transaction_date
      AND t.rank_52w_loss = mrpd.max_rank
    WHERE t.company_name IS NOT NULL -- Company name must be present
      AND t.pct_loss_52w IS NOT NULL -- Percentage loss value must be present
      AND NOT IS_NAN(t.pct_loss_52w) -- Percentage loss must not be NaN
      AND t.pct_loss_52w < 0 -- Re-confirm it's a loss; this should be true if rank logic is consistent
  )
  SELECT 
    transaction_date AS date_lowest_52w,
    company_name,
    pct_loss_52w
  FROM TopLoserPerDate
  WHERE rn = 1 -- Select the designated top loser for each date (after tie-breaking)
  ORDER BY RAND() -- Randomly select 5 such top loser records, each from a different date
  LIMIT 5;