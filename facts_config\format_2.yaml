fact_id: fact_2

fact_format: |
  On trading [Week Number] ending on [Date], [Company X] achieved the highest traded value in one week for the past [N] years.

required_tables:
  - positive-theme-384616.standarized_dataset.stock_data

metrics:
  - name: date_highest_period
    type: DATE
    description: The end date of the week when the company reached its highest turnover
  - name: company_name
    type: STRING
    description: Name of the company that reached the highest turnover
  - name: week_number
    type: STRING
    description: The week number extracted from the year_week column (e.g., "01" from "2023-W01"), representing the specific week within the year when the highest turnover occurred
  - name: lookback_years
    type: INTEGER
    description: The number of full years (N) in the lookback period for this company

analysis_description: |
  The Weekly High Analysis Process starts by analyzing the data availability for each company,
  identifying only those with at least 1 full year (52 weeks) of transaction data.
  Instead of requiring the data to start exactly on January 1, 2023, the process accepts any continuous 52-week (or longer) period,
  calculating the precise lookback period in years (N) available for each company.
  The analysis then organizes each qualifying company's data into complete weekly periods,
  computes the total turnover for each week, and identifies the week with the highest cumulative traded value.
  This approach ensures inclusion of all companies meeting the minimum 1-year threshold, regardless of when their data began,
  while accurately reporting the exact lookback period used in the analysis.

  The generated SQL query will return 5 rows - 5 companies selected randomly - with each row containing the week number, the week's ending date
  when that specific company achieved its highest weekly turnover, and the number of full lookback_years years (N). 
  The query will ensure all returned rows have complete data with no NULL or NaN values in any field.

sql_query: |
  -- This query identifies companies that achieved their highest weekly traded value (turnover)
  -- within their available trading history, considering only companies with at least one full year (52 weeks) of data.
  -- It returns the week's end date, company name, the specific week number of the peak,
  -- and the lookback period in full years (N) for that company.
  -- The final result provides this information for 5 randomly selected companies.
  WITH -- Step 1: Calculate data span for each company to determine eligibility and lookback period.
  -- Companies must have at least 52 weeks of data.
  -- Lookback years (N) are calculated as full years of data available.
  CompanyDataSpan AS (
    SELECT ticker_id, 
           company_name,
           -- Calculate lookback_years as an INTEGER, representing full 52-week periods.
           -- BigQuery's INT64 / INT64 division results in FLOAT64, so FLOOR works as expected.
           CAST(FLOOR(COUNT(DISTINCT year_week) / 52.0) AS INTEGER) AS lookback_years
    FROM `positive-theme-384616.standarized_dataset.stock_data`
    WHERE -- Ensure essential fields for grouping and calculation are not null
      ticker_id IS NOT NULL
      AND company_name IS NOT NULL
      AND year_week IS NOT NULL
    GROUP BY ticker_id,
             company_name
    HAVING COUNT(DISTINCT year_week) >= 52 -- Filter for companies with at least 1 full year (52 weeks) of data
  ),
  -- Step 2: Calculate weekly turnover and identify the end date for each week.
  -- This aggregates daily transaction data into weekly figures.
  CompanyWeeklyTurnover AS (
    SELECT company_name,
           ticker_id,
           year_week,
           -- Extract the week number (e.g., "01" from "2023-W01"). Assumes "YYYY-Www" format.
           SUBSTR(year_week, STRPOS(year_week, '-W') + 2) AS week_number_str,
           MAX(transaction_date) AS week_end_date, -- The last trading day of that year_week
           SUM(turnover) AS weekly_turnover
    FROM `positive-theme-384616.standarized_dataset.stock_data`
    WHERE -- Ensure essential fields for aggregation are not null
      ticker_id IS NOT NULL
      AND company_name IS NOT NULL
      AND year_week IS NOT NULL
      AND transaction_date IS NOT NULL
      AND turnover IS NOT NULL
    GROUP BY company_name,
             ticker_id,
             year_week
  ),
  -- Step 3: Rank weekly turnovers for each eligible company to find the week with the highest turnover.
  -- Joins with CompanyDataSpan to consider only companies meeting the data availability criteria.
  RankedWeeklyTurnover AS (
    SELECT cwt.company_name,
           cwt.ticker_id,
           cwt.week_number_str,
           cwt.week_end_date,
           cwt.weekly_turnover,
           cds.lookback_years,
           -- Rank weeks by turnover in descending order. Tie-break with the most recent week_end_date.
           ROW_NUMBER() OVER (PARTITION BY cwt.ticker_id
                             ORDER BY cwt.weekly_turnover DESC, cwt.week_end_date DESC) AS rn
    FROM CompanyWeeklyTurnover cwt
    INNER JOIN CompanyDataSpan cds ON cwt.ticker_id = cds.ticker_id
      AND cwt.company_name = cds.company_name -- Ensure joining on the same company definition
    WHERE cwt.weekly_turnover > 0 -- Consider only weeks with positive turnover
  )
  -- Final Step: Select the required metrics for the week with the highest turnover (rn=1)
  -- for 5 randomly selected companies.
  -- The construction of CTEs and their WHERE clauses ensure that all selected fields will be non-NULL.
  SELECT rwt.week_end_date AS date_highest_period,
         rwt.company_name,
         rwt.week_number_str AS week_number,
         rwt.lookback_years
  FROM RankedWeeklyTurnover rwt
  WHERE rwt.rn = 1
  ORDER BY RAND() -- Select 5 companies randomly
  LIMIT 5;
