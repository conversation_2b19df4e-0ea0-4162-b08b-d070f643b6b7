# fact_1.yaml

fact_id: fact_1

fact_format: |
  On [Date], [Company X] reached its highest trading value in 52 weeks, with an increase of [Z]% from the previous day, and [Y]% from the beginning of [year]

required_tables:
  - positive-theme-384616.standarized_dataset.stock_data

metrics:
  - name: date_highest_52w
    type: DATE
    description: The date within the 52-week window when the highest trading value occurred
  - name: company_name
    type: STRING
    description: Name of the company
  - name: pct_change_prev
    type: FLOAT
    description: The percentage change in turnover from the previous day
  - name: pct_change_start_of_year
    type: FLOAT
    description: The percentage change in turnover since the beginning of that year. Calculated as (turnover - turnover at the beginning of the year) / turnover at the beginning of the year

analysis_description: |
  The 52-Week High Analysis Process identifies each company's peak trading performance by
  starting from the latest transaction date and examining all data points within the previous 52 weeks.
  The analysis locates the specific date when each company achieved its highest turnover within this window,
  then calculates two key performance metrics: the percentage change in turnover from the trading day immediately
  before the peak date and the year-to-date percentage change in turnover from the beginning of the year in which the peak occurred.

  The generated SQL query will return 5 rows - 5 companies selected randomly - with each row containing
  the date when that specific company reached its highest trading value in the 52-week period but also make sure the returned rows don't have
  any NaN value in it.

sql_query: |
  -- Find the latest transaction date in the dataset to define the end of the 52-week window
  WITH LatestDate AS (
    SELECT MAX(transaction_date) AS max_date
    FROM `positive-theme-384616.standarized_dataset.stock_data`
  ),
  -- Calculate previous day turnover and the turnover at the start of the year for each stock on each day
  DataWithPrevAndYearStart AS (
    SELECT 
      ticker_id,
      company_name,
      transaction_date,
      turnover,
      -- Get the turnover from the previous trading day for the same stock
      LAG(turnover, 1) OVER (PARTITION BY ticker_id ORDER BY transaction_date) AS prev_day_turnover,
      -- Get the turnover from the first trading day of the year for the same stock
      FIRST_VALUE(turnover) OVER (
        PARTITION BY ticker_id, EXTRACT(YEAR FROM transaction_date)
        ORDER BY transaction_date ASC 
        ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
      ) AS start_of_year_turnover,
      EXTRACT(YEAR FROM transaction_date) AS year_of_date
    FROM `positive-theme-384616.standarized_dataset.stock_data`
  ),
  -- Identify the date within the last 52 weeks (364 days) where each company had its highest turnover
  WindowPeak AS (
    SELECT 
      d.ticker_id,
      d.company_name,
      d.transaction_date,
      d.turnover,
      d.prev_day_turnover,
      d.start_of_year_turnover,
      d.year_of_date,
      -- Rank records within the 52-week window for each company based on turnover (highest first)
      -- Tie-breaking using the most recent date in case of identical peak turnover values
      ROW_NUMBER() OVER (PARTITION BY d.ticker_id ORDER BY d.turnover DESC, d.transaction_date DESC) AS rn
    FROM DataWithPrevAndYearStart d
    CROSS JOIN LatestDate ld
    -- Filter data to include only the last 52 weeks from the most recent record date
    WHERE d.transaction_date BETWEEN DATE_SUB(ld.max_date, INTERVAL 364 DAY) AND ld.max_date
  )
  -- Final selection: Extract the required metrics for companies on their 52-week peak turnover date
  SELECT 
    wp.transaction_date AS date_highest_52w,
    wp.company_name,
    -- Calculate percentage change from the previous day's turnover, rounded to 2 decimal places
    ROUND(SAFE_DIVIDE(wp.turnover - wp.prev_day_turnover, wp.prev_day_turnover) * 100, 2) AS pct_change_prev,
    -- Calculate percentage change from the start of the year's turnover, rounded to 2 decimal places
    ROUND(SAFE_DIVIDE(wp.turnover - wp.start_of_year_turnover, wp.start_of_year_turnover) * 100, 2) AS pct_change_start_of_year
  FROM WindowPeak wp
  WHERE wp.rn = 1  -- Select only the row representing the absolute peak turnover within the 52-week window
    -- Ensure that the denominators for percentage calculations are non-null and non-zero
    AND wp.prev_day_turnover IS NOT NULL
    AND wp.prev_day_turnover != 0
    AND wp.start_of_year_turnover IS NOT NULL
    AND wp.start_of_year_turnover != 0
    -- Ensure the calculated percentage values are not NULL (SAFE_DIVIDE returns NULL on error)
    AND SAFE_DIVIDE(wp.turnover - wp.prev_day_turnover, wp.prev_day_turnover) IS NOT NULL
    AND SAFE_DIVIDE(wp.turnover - wp.start_of_year_turnover, wp.start_of_year_turnover) IS NOT NULL
    -- Explicitly filter out any potential NaN results from the division
    AND NOT IS_NAN(SAFE_DIVIDE(wp.turnover - wp.prev_day_turnover, wp.prev_day_turnover))
    AND NOT IS_NAN(SAFE_DIVIDE(wp.turnover - wp.start_of_year_turnover, wp.start_of_year_turnover))
  -- Order randomly to select 5 sample companies meeting the criteria
  ORDER BY RAND()
  LIMIT 5;
# fact_1.yaml

fact_id: fact_1

fact_format: |
  On [Date], [Company X] reached its highest trading value in 52 weeks, with an increase of [Z]% from the previous day, and [Y]% from the beginning of [year]

required_tables:
  - positive-theme-384616.standarized_dataset.stock_data

metrics:
  - name: date_highest_52w
    type: DATE
    description: The date within the 52-week window when the highest trading value occurred
  - name: company_name
    type: STRING
    description: Name of the company
  - name: pct_change_prev
    type: FLOAT
    description: The percentage change in turnover from the previous day
  - name: pct_change_start_of_year
    type: FLOAT
    description: The percentage change in turnover since the beginning of that year. Calculated as (turnover - turnover at the beginning of the year) / turnover at the beginning of the year

analysis_description: |
  The 52-Week High Analysis Process identifies each company's peak trading performance by
  starting from the latest transaction date and examining all data points within the previous 52 weeks.
  The analysis locates the specific date when each company achieved its highest turnover within this window,
  then calculates two key performance metrics: the percentage change in turnover from the trading day immediately
  before the peak date and the year-to-date percentage change in turnover from the beginning of the year in which the peak occurred.

  The generated SQL query will return 5 rows - 5 companies selected randomly - with each row containing
  the date when that specific company reached its highest trading value in the 52-week period but also make sure the returned rows don't have
  any NaN value in it.

sql_query: |
  -- Find the latest transaction date in the dataset to define the end of the 52-week window
  WITH LatestDate AS (
    SELECT MAX(transaction_date) AS max_date
    FROM `positive-theme-384616.standarized_dataset.stock_data`
  ),
  -- Calculate previous day turnover and the turnover at the start of the year for each stock on each day
  DataWithPrevAndYearStart AS (
    SELECT 
      ticker_id,
      company_name,
      transaction_date,
      turnover,
      -- Get the turnover from the previous trading day for the same stock
      LAG(turnover, 1) OVER (PARTITION BY ticker_id ORDER BY transaction_date) AS prev_day_turnover,
      -- Get the turnover from the first trading day of the year for the same stock
      FIRST_VALUE(turnover) OVER (
        PARTITION BY ticker_id, EXTRACT(YEAR FROM transaction_date)
        ORDER BY transaction_date ASC 
        ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
      ) AS start_of_year_turnover,
      EXTRACT(YEAR FROM transaction_date) AS year_of_date
    FROM `positive-theme-384616.standarized_dataset.stock_data`
  ),
  -- Identify the date within the last 52 weeks (364 days) where each company had its highest turnover
  WindowPeak AS (
    SELECT 
      d.ticker_id,
      d.company_name,
      d.transaction_date,
      d.turnover,
      d.prev_day_turnover,
      d.start_of_year_turnover,
      d.year_of_date,
      -- Rank records within the 52-week window for each company based on turnover (highest first)
      -- Tie-breaking using the most recent date in case of identical peak turnover values
      ROW_NUMBER() OVER (PARTITION BY d.ticker_id ORDER BY d.turnover DESC, d.transaction_date DESC) AS rn
    FROM DataWithPrevAndYearStart d
    CROSS JOIN LatestDate ld
    -- Filter data to include only the last 52 weeks from the most recent record date
    WHERE d.transaction_date BETWEEN DATE_SUB(ld.max_date, INTERVAL 364 DAY) AND ld.max_date
  )
  -- Final selection: Extract the required metrics for companies on their 52-week peak turnover date
  SELECT 
    wp.transaction_date AS date_highest_52w,
    wp.company_name,
    -- Calculate percentage change from the previous day's turnover, rounded to 2 decimal places
    ROUND(SAFE_DIVIDE(wp.turnover - wp.prev_day_turnover, wp.prev_day_turnover) * 100, 2) AS pct_change_prev,
    -- Calculate percentage change from the start of the year's turnover, rounded to 2 decimal places
    ROUND(SAFE_DIVIDE(wp.turnover - wp.start_of_year_turnover, wp.start_of_year_turnover) * 100, 2) AS pct_change_start_of_year
  FROM WindowPeak wp
  WHERE wp.rn = 1  -- Select only the row representing the absolute peak turnover within the 52-week window
    -- Ensure that the denominators for percentage calculations are non-null and non-zero
    AND wp.prev_day_turnover IS NOT NULL
    AND wp.prev_day_turnover != 0
    AND wp.start_of_year_turnover IS NOT NULL
    AND wp.start_of_year_turnover != 0
    -- Ensure the calculated percentage values are not NULL (SAFE_DIVIDE returns NULL on error)
    AND SAFE_DIVIDE(wp.turnover - wp.prev_day_turnover, wp.prev_day_turnover) IS NOT NULL
    AND SAFE_DIVIDE(wp.turnover - wp.start_of_year_turnover, wp.start_of_year_turnover) IS NOT NULL
    -- Explicitly filter out any potential NaN results from the division
    AND NOT IS_NAN(SAFE_DIVIDE(wp.turnover - wp.prev_day_turnover, wp.prev_day_turnover))
    AND NOT IS_NAN(SAFE_DIVIDE(wp.turnover - wp.start_of_year_turnover, wp.start_of_year_turnover))
    AND wp.transaction_date >= DATE_SUB(ld.max_date, INTERVAL 3 MONTH)
  -- Order randomly to select 5 sample companies meeting the criteria
  ORDER BY RAND()
  LIMIT 5;
