fact_id: fact_8

fact_format: |
  On [Date], [Company X] was the top gainer in terms of % for the past 52 weeks with a last P/E ratio [Y].

required_tables:
  - positive-theme-384616.standarized_dataset.stock_data
  - positive-theme-384616.standarized_dataset.pct_top_gainers

metrics:
  - name: date_highest_52w
    type: DATE
    description: The date within the past 52 weeks (from latest transaction_date) when the company is top gainer
  - name: company_name
    type: STRING
    description: Name of the company which is top gainer
  - name: pct_gain_52w
    type: FLOAT
    description: Percentage increase in the last 52 weeks
  - name: pe_ratio
    type: FLOAT
    description: P/E ratio of that company on that date


analysis_description: |
  First, we identify the top-gaining company for each transaction date by filtering the {top_gainer_table_id} where rank_52w_gain = 1.
  Next, we join this result with the {gtn_data_table_id} using both ticker_id (or company_name) AND transaction_date as matching criteria to ensure
  we retrieve the P/E ratio specific to that exact date. The analysis selects five random transaction dates from {top_gainer_table_id} and performs
  this precise date-matched join operation for each selected date. It should be make sure we do not return the same company in the result.
  The process verifies that percentage gain values are positive and date-specific P/E ratios exist in the {gtn_data_table_id}.

  The generated SQL query must return 5 rows - 5 different top gainer companies from different dates. 
  The query will ensure all returned rows have complete data with no NULL or NaN values in any field.

sql_query: |
  WITH TopGainersWithPE AS (
    SELECT 
      tg.transaction_date AS date_highest_52w,
      tg.company_name,
      tg.ticker_id,
      tg.pct_gain_52w,
      sd.pe_ratio AS pe_ratio_str -- Keep pe_ratio as string temporarily for robust casting later
    FROM `positive-theme-384616.standarized_dataset.pct_top_gainers` AS tg
    JOIN `positive-theme-384616.standarized_dataset.stock_data` AS sd 
      ON tg.ticker_id = sd.ticker_id
      AND tg.transaction_date = sd.transaction_date
    WHERE tg.rank_52w_gain = 1 -- Company is the top gainer
      AND tg.pct_gain_52w > 0 -- Percentage gain is positive
      AND sd.pe_ratio IS NOT NULL -- P/E ratio exists
      AND SAFE_CAST(sd.pe_ratio AS FLOAT64) IS NOT NULL -- P/E ratio is a valid number
      AND tg.transaction_date >= DATE_SUB((SELECT MAX(transaction_date) FROM positive-theme-384616.standarized_dataset.pct_top_gainers), INTERVAL 3 MONTH)
  ),
  DistinctCompanyTopGainerEvents AS (
    SELECT 
      date_highest_52w,
      company_name,
      ticker_id, -- Retained for completeness, though not in final output columns
      pct_gain_52w,
      SAFE_CAST(pe_ratio_str AS FLOAT64) AS pe_ratio,
      ROW_NUMBER() OVER (
        PARTITION BY company_name
        ORDER BY date_highest_52w DESC
      ) AS rn
    FROM TopGainersWithPE
  )
  SELECT 
    date_highest_52w,
    company_name,
    ROUND(pct_gain_52w, 2) AS pct_gain_52w,
    ROUND(pe_ratio, 2) AS pe_ratio
  FROM DistinctCompanyTopGainerEvents
  WHERE rn = 1 -- Filter to get only one (the latest, as per ORDER BY) top gainer event per company
  ORDER BY RAND() -- Randomly order the resulting distinct company events
  LIMIT 5; -- Select the top 5 random distinct companies